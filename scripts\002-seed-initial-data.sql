-- Seed initial data for SynapseAI

-- Create default organization
INSERT INTO organizations (id, name, slug, subscription_tier) 
VALUES ('00000000-0000-0000-0000-000000000001', 'SynapseAI Demo', 'demo', 'pro');

-- Create system roles
INSERT INTO roles (id, organization_id, name, description, permissions, is_system) VALUES
('00000000-0000-0000-0000-000000000001', '00000000-0000-0000-0000-000000000001', 'Super Admin', 'Full system access', '["*"]', true),
('00000000-0000-0000-0000-000000000002', '00000000-0000-0000-0000-000000000001', 'Admin', 'Organization admin', '["agents.*", "tools.*", "users.*", "analytics.*"]', true),
('00000000-0000-0000-0000-000000000003', '00000000-0000-0000-0000-000000000001', 'Developer', 'Agent and tool development', '["agents.*", "tools.*", "sandboxes.*"]', true),
('00000000-0000-0000-0000-000000000004', '00000000-0000-0000-0000-000000000001', 'Approver', 'HITL approval access', '["hitl.*", "agents.read", "tools.read"]', true),
('00000000-0000-0000-0000-000000000005', '00000000-0000-0000-0000-000000000001', 'Viewer', 'Read-only access', '["*.read"]', true);

-- Create demo admin user (password: admin123)
INSERT INTO users (id, organization_id, email, password_hash, first_name, last_name, is_active, email_verified) 
VALUES ('00000000-0000-0000-0000-000000000001', '00000000-0000-0000-0000-000000000001', '<EMAIL>', '$2b$10$rQZ8kHWfQxwjKjB5.Nq8/.vQZ8kHWfQxwjKjB5.Nq8/.vQZ8kHWfQx', 'Admin', 'User', true, true);

-- Assign admin role
INSERT INTO user_roles (user_id, role_id) 
VALUES ('00000000-0000-0000-0000-000000000001', '00000000-0000-0000-0000-000000000001');

-- Create sample providers
INSERT INTO providers (id, organization_id, name, type, config, created_by) VALUES
('00000000-0000-0000-0000-000000000001', '00000000-0000-0000-0000-000000000001', 'OpenAI GPT-4', 'openai', '{"model": "gpt-4", "temperature": 0.7}', '00000000-0000-0000-0000-000000000001'),
('00000000-0000-0000-0000-000000000002', '00000000-0000-0000-0000-000000000001', 'Anthropic Claude', 'anthropic', '{"model": "claude-3-sonnet", "temperature": 0.7}', '00000000-0000-0000-0000-000000000001');

-- Create sample tools
INSERT INTO tools (id, organization_id, name, description, type, schema, implementation, created_by) VALUES
('00000000-0000-0000-0000-000000000001', '00000000-0000-0000-0000-000000000001', 'Web Search', 'Search the web for information', 'function', 
'{"type": "function", "function": {"name": "web_search", "parameters": {"type": "object", "properties": {"query": {"type": "string"}}}}}',
'{"endpoint": "/api/tools/web-search", "method": "POST"}', '00000000-0000-0000-0000-000000000001'),
('00000000-0000-0000-0000-000000000002', '00000000-0000-0000-0000-000000000001', 'Calculator', 'Perform mathematical calculations', 'function',
'{"type": "function", "function": {"name": "calculate", "parameters": {"type": "object", "properties": {"expression": {"type": "string"}}}}}',
'{"code": "function calculate(expression) { return eval(expression); }"}', '00000000-0000-0000-0000-000000000001');

-- Create sample agent
INSERT INTO agents (id, organization_id, name, description, system_prompt, model, provider_id, status, created_by) VALUES
('00000000-0000-0000-0000-000000000001', '00000000-0000-0000-0000-000000000001', 'General Assistant', 'A helpful AI assistant', 
'You are a helpful AI assistant. Use the available tools to help users with their requests.', 'gpt-4', 
'00000000-0000-0000-0000-000000000001', 'active', '00000000-0000-0000-0000-000000000001');

-- Link tools to agent
INSERT INTO agent_tools (agent_id, tool_id) VALUES
('00000000-0000-0000-0000-000000000001', '00000000-0000-0000-0000-000000000001'),
('00000000-0000-0000-0000-000000000001', '00000000-0000-0000-0000-000000000002');

-- Create sample widget
INSERT INTO widgets (id, organization_id, name, description, type, config, agent_id, created_by) VALUES
('00000000-0000-0000-0000-000000000001', '00000000-0000-0000-0000-000000000001', 'Chat Widget', 'Embeddable chat widget', 'chat',
'{"theme": "light", "position": "bottom-right", "greeting": "Hello! How can I help you?"}',
'00000000-0000-0000-0000-000000000001', '00000000-0000-0000-0000-000000000001');
