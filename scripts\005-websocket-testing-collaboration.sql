-- WebSocket Testing Collaboration Schema

-- Test Scenarios table
CREATE TABLE IF NOT EXISTS test_scenarios (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    organization_id UUID NOT NULL REFERENCES organizations(id) ON DELETE CASCADE,
    name VARCHAR(255) NOT NULL,
    description TEXT,
    category VARCHAR(100) NOT NULL DEFAULT 'general',
    config JSONB NOT NULL DEFAULT '{}',
    connections JSONB NOT NULL DEFAULT '[]',
    messages JSONB NOT NULL DEFAULT '[]',
    assertions JSONB NOT NULL DEFAULT '[]',
    status VARCHAR(50) NOT NULL DEFAULT 'draft',
    version INTEGER NOT NULL DEFAULT 1,
    is_template BOOLEAN NOT NULL DEFAULT false,
    is_public BOOLEAN NOT NULL DEFAULT false,
    created_by UUID NOT NULL REFERENCES users(id),
    updated_by UUID REFERENCES users(id),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- Scenario Versions for history tracking
CREATE TABLE IF NOT EXISTS test_scenario_versions (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    scenario_id UUID NOT NULL REFERENCES test_scenarios(id) ON DELETE CASCADE,
    version_number INTEGER NOT NULL,
    name VARCHAR(255) NOT NULL,
    description TEXT,
    config JSONB NOT NULL DEFAULT '{}',
    connections JSONB NOT NULL DEFAULT '[]',
    messages JSONB NOT NULL DEFAULT '[]',
    assertions JSONB NOT NULL DEFAULT '[]',
    change_summary TEXT,
    created_by UUID NOT NULL REFERENCES users(id),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    UNIQUE(scenario_id, version_number)
);

-- Scenario Sharing and Permissions
CREATE TABLE IF NOT EXISTS test_scenario_shares (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    scenario_id UUID NOT NULL REFERENCES test_scenarios(id) ON DELETE CASCADE,
    shared_with_user_id UUID REFERENCES users(id) ON DELETE CASCADE,
    shared_with_role_id UUID REFERENCES roles(id) ON DELETE CASCADE,
    permission_level VARCHAR(50) NOT NULL DEFAULT 'read', -- read, write, admin
    shared_by UUID NOT NULL REFERENCES users(id),
    expires_at TIMESTAMP WITH TIME ZONE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    CONSTRAINT check_shared_with CHECK (
        (shared_with_user_id IS NOT NULL AND shared_with_role_id IS NULL) OR
        (shared_with_user_id IS NULL AND shared_with_role_id IS NOT NULL)
    )
);

-- Scenario Comments and Discussions
CREATE TABLE IF NOT EXISTS test_scenario_comments (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    scenario_id UUID NOT NULL REFERENCES test_scenarios(id) ON DELETE CASCADE,
    parent_comment_id UUID REFERENCES test_scenario_comments(id) ON DELETE CASCADE,
    content TEXT NOT NULL,
    content_type VARCHAR(50) NOT NULL DEFAULT 'text', -- text, markdown
    is_resolved BOOLEAN NOT NULL DEFAULT false,
    created_by UUID NOT NULL REFERENCES users(id),
    updated_by UUID REFERENCES users(id),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- Scenario Favorites
CREATE TABLE IF NOT EXISTS test_scenario_favorites (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    scenario_id UUID NOT NULL REFERENCES test_scenarios(id) ON DELETE CASCADE,
    user_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    UNIQUE(scenario_id, user_id)
);

-- Scenario Tags
CREATE TABLE IF NOT EXISTS test_scenario_tags (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    scenario_id UUID NOT NULL REFERENCES test_scenarios(id) ON DELETE CASCADE,
    tag VARCHAR(100) NOT NULL,
    created_by UUID NOT NULL REFERENCES users(id),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    UNIQUE(scenario_id, tag)
);

-- Real-time Collaboration Sessions
CREATE TABLE IF NOT EXISTS test_scenario_sessions (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    scenario_id UUID NOT NULL REFERENCES test_scenarios(id) ON DELETE CASCADE,
    user_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    session_token VARCHAR(255) NOT NULL,
    cursor_position JSONB,
    last_activity TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    UNIQUE(scenario_id, user_id)
);

-- Indexes for performance
CREATE INDEX IF NOT EXISTS idx_test_scenarios_organization_id ON test_scenarios(organization_id);
CREATE INDEX IF NOT EXISTS idx_test_scenarios_created_by ON test_scenarios(created_by);
CREATE INDEX IF NOT EXISTS idx_test_scenarios_status ON test_scenarios(status);
CREATE INDEX IF NOT EXISTS idx_test_scenarios_category ON test_scenarios(category);
CREATE INDEX IF NOT EXISTS idx_test_scenarios_is_public ON test_scenarios(is_public);

CREATE INDEX IF NOT EXISTS idx_test_scenario_versions_scenario_id ON test_scenario_versions(scenario_id);
CREATE INDEX IF NOT EXISTS idx_test_scenario_versions_version_number ON test_scenario_versions(scenario_id, version_number);

CREATE INDEX IF NOT EXISTS idx_test_scenario_shares_scenario_id ON test_scenario_shares(scenario_id);
CREATE INDEX IF NOT EXISTS idx_test_scenario_shares_user_id ON test_scenario_shares(shared_with_user_id);
CREATE INDEX IF NOT EXISTS idx_test_scenario_shares_role_id ON test_scenario_shares(shared_with_role_id);

CREATE INDEX IF NOT EXISTS idx_test_scenario_comments_scenario_id ON test_scenario_comments(scenario_id);
CREATE INDEX IF NOT EXISTS idx_test_scenario_comments_parent_id ON test_scenario_comments(parent_comment_id);

CREATE INDEX IF NOT EXISTS idx_test_scenario_favorites_user_id ON test_scenario_favorites(user_id);
CREATE INDEX IF NOT EXISTS idx_test_scenario_tags_scenario_id ON test_scenario_tags(scenario_id);
CREATE INDEX IF NOT EXISTS idx_test_scenario_sessions_scenario_id ON test_scenario_sessions(scenario_id);

-- Triggers for updated_at
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = CURRENT_TIMESTAMP;
    RETURN NEW;
END;
$$ language 'plpgsql';

CREATE TRIGGER update_test_scenarios_updated_at BEFORE UPDATE ON test_scenarios FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_test_scenario_comments_updated_at BEFORE UPDATE ON test_scenario_comments FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
