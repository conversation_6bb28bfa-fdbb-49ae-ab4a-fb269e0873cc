{"name": "@synapseai/dashboard", "version": "1.0.0", "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint"}, "dependencies": {"next": "^14.0.4", "react": "^18.2.0", "react-dom": "^18.2.0", "@tanstack/react-query": "^5.17.0", "@radix-ui/react-slot": "^1.0.2", "@radix-ui/react-dropdown-menu": "^2.0.6", "@radix-ui/react-toast": "^1.1.5", "class-variance-authority": "^0.7.0", "clsx": "^2.0.0", "tailwind-merge": "^2.2.0", "lucide-react": "^0.303.0", "next-themes": "^0.2.1"}, "devDependencies": {"@types/node": "^20.10.5", "@types/react": "^18.2.45", "@types/react-dom": "^18.2.18", "autoprefixer": "^10.4.16", "postcss": "^8.4.32", "tailwindcss": "^3.4.0", "typescript": "^5.3.3", "eslint": "^8.56.0", "eslint-config-next": "^14.0.4"}}