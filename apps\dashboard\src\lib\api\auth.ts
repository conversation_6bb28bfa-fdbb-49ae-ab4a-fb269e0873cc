import { apiClient } from "./client"
import type { User, AuthTokens } from "@synapseai/shared/types"

export const authApi = {
  // Authentication
  login: (credentials: { email: string; password: string }) => apiClient.post<AuthTokens>("/auth/login", credentials),

  register: (userData: {
    email: string
    password: string
    firstName: string
    lastName: string
    organizationId: string
  }) => apiClient.post<AuthTokens>("/auth/register", userData),

  logout: () => apiClient.post("/auth/logout"),

  refreshToken: (refreshToken: string) => apiClient.post<AuthTokens>("/auth/refresh", { refreshToken }),

  getProfile: () => apiClient.post<User>("/auth/me"),

  updateProfile: (data: Partial<User>) => apiClient.put<User>("/auth/profile", data),

  changePassword: (data: { currentPassword: string; newPassword: string }) =>
    apiClient.post("/auth/change-password", data),

  // Password Reset
  requestPasswordReset: (email: string) => apiClient.post("/auth/forgot-password", { email }),

  resetPassword: (data: { token: string; password: string }) => apiClient.post("/auth/reset-password", data),

  // Email Verification
  sendVerificationEmail: () => apiClient.post("/auth/send-verification"),

  verifyEmail: (token: string) => apiClient.post("/auth/verify-email", { token }),

  // Two-Factor Authentication
  enableTwoFactor: () => apiClient.post("/auth/2fa/enable"),

  disableTwoFactor: (code: string) => apiClient.post("/auth/2fa/disable", { code }),

  verifyTwoFactor: (code: string) => apiClient.post("/auth/2fa/verify", { code }),

  // API Keys
  createApiKey: (data: { name: string; permissions: string[]; expiresAt?: string }) =>
    apiClient.post("/auth/api-keys", data),

  getApiKeys: () => apiClient.get("/auth/api-keys"),

  revokeApiKey: (keyId: string) => apiClient.delete(`/auth/api-keys/${keyId}`),

  // SSO
  getSSOProviders: () => apiClient.get("/auth/sso/providers"),

  initiateSSOLogin: (providerId: string) => apiClient.post(`/auth/sso/${providerId}/login`),

  // Permissions & Quotas
  getPermissions: () => apiClient.get("/auth/permissions"),

  checkQuota: (resource: string) => apiClient.get(`/auth/quota/${resource}`),

  getUsage: () => apiClient.get("/auth/usage"),

  // Sessions
  getSessions: () => apiClient.get("/auth/sessions"),

  revokeSession: (sessionId: string) => apiClient.delete(`/auth/sessions/${sessionId}`),

  revokeAllSessions: () => apiClient.delete("/auth/sessions"),
}
