import { apiClient } from "./client"

export const dashboardApi = {
  getStats: async () => {
    const response = await apiClient.get("/dashboard/stats")
    return response.data
  },

  getRecentActivity: async () => {
    const response = await apiClient.get("/dashboard/activity")
    return response.data
  },

  getPendingApprovals: async () => {
    const response = await apiClient.get("/hitl?status=pending&limit=5")
    return response.data
  },
}
