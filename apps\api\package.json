{"name": "@synapseai/api", "version": "1.0.0", "scripts": {"build": "nest build", "start": "nest start", "start:dev": "nest start --watch", "start:prod": "node dist/main", "db:migrate": "node scripts/migrate.js", "db:seed": "node scripts/seed.js"}, "dependencies": {"@nestjs/common": "^10.3.0", "@nestjs/core": "^10.3.0", "@nestjs/config": "^3.1.1", "@nestjs/typeorm": "^10.0.1", "@nestjs/jwt": "^10.2.0", "@nestjs/passport": "^10.0.2", "@nestjs/swagger": "^7.1.17", "@nestjs/websockets": "^10.3.0", "@nestjs/platform-ws": "^10.3.0", "@nestjs-modules/ioredis": "^2.0.2", "typeorm": "^0.3.17", "pg": "^8.11.3", "ioredis": "^5.3.2", "bcrypt": "^5.1.1", "passport": "^0.7.0", "passport-jwt": "^4.0.1", "class-validator": "^0.14.0", "class-transformer": "^0.5.1", "ws": "^8.16.0", "reflect-metadata": "^0.1.13", "rxjs": "^7.8.1"}, "devDependencies": {"@nestjs/cli": "^10.2.1", "@types/node": "^20.10.5", "@types/bcrypt": "^5.0.2", "@types/passport-jwt": "^3.0.13", "@types/ws": "^8.5.10", "typescript": "^5.3.3"}}