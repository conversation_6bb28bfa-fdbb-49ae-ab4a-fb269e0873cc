import { apiClient } from "./client"

export const testingApi = {
  // Test Scenarios
  createScenario: (data: any) => apiClient.post("/testing/scenarios", data),
  getScenarios: (params?: any) => apiClient.get(`/testing/scenarios?${new URLSearchParams(params)}`),
  getScenario: (id: string) => apiClient.get(`/testing/scenarios/${id}`),
  updateScenario: (id: string, data: any) => apiClient.put(`/testing/scenarios/${id}`, data),
  deleteScenario: (id: string) => apiClient.delete(`/testing/scenarios/${id}`),

  // Test Executions
  executeScenario: (id: string, data: any) => apiClient.post(`/testing/scenarios/${id}/execute`, data),
  getExecutions: (params?: any) => apiClient.get(`/testing/executions?${new URLSearchParams(params)}`),
  getExecution: (id: string) => apiClient.get(`/testing/executions/${id}`),
  cancelExecution: (id: string) => apiClient.post(`/testing/executions/${id}/cancel`),

  // Event Templates
  createTemplate: (data: any) => apiClient.post("/testing/templates", data),
  getTemplates: () => apiClient.get("/testing/templates"),
  getTemplate: (id: string) => apiClient.get(`/testing/templates/${id}`),
  updateTemplate: (id: string, data: any) => apiClient.put(`/testing/templates/${id}`, data),
  deleteTemplate: (id: string) => apiClient.delete(`/testing/templates/${id}`),

  // Test Reports
  generateReport: (data: any) => apiClient.post("/testing/reports", data),
  getReports: (params?: any) => apiClient.get(`/testing/reports?${new URLSearchParams(params)}`),
  getReport: (id: string) => apiClient.get(`/testing/reports/${id}`),

  // Test Monitors
  createMonitor: (data: any) => apiClient.post("/testing/monitors", data),
  getMonitors: () => apiClient.get("/testing/monitors"),
  updateMonitor: (id: string, data: any) => apiClient.put(`/testing/monitors/${id}`, data),
  deleteMonitor: (id: string) => apiClient.delete(`/testing/monitors/${id}`),
}
