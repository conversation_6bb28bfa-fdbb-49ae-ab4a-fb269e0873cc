-- Event Testing Environment Database Schema

-- Test Scenarios for organizing test cases
CREATE TABLE test_scenarios (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    organization_id UUID REFERENCES organizations(id) ON DELETE CASCADE,
    name VA<PERSON><PERSON><PERSON>(255) NOT NULL,
    description TEXT,
    category VARCHAR(100) NOT NULL, -- 'agents', 'tools', 'hitl', 'notifications', etc.
    config JSONB NOT NULL DEFAULT '{}',
    expected_events JSONB DEFAULT '[]',
    status VARCHAR(50) DEFAULT 'draft', -- 'draft', 'active', 'completed', 'failed'
    created_by UUID REFERENCES users(id),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Test Executions for tracking test runs
CREATE TABLE test_executions (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    organization_id UUID REFERENCES organizations(id) ON DELETE CASCADE,
    scenario_id UUID REFERENCES test_scenarios(id) ON DELETE CASCADE,
    name VA<PERSON>HA<PERSON>(255) NOT NULL,
    status VARCHAR(50) DEFAULT 'running', -- 'running', 'completed', 'failed', 'cancelled'
    start_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    end_time TIMESTAMP,
    duration_ms INTEGER,
    events_generated INTEGER DEFAULT 0,
    events_received INTEGER DEFAULT 0,
    success_rate DECIMAL(5,2) DEFAULT 0,
    error_count INTEGER DEFAULT 0,
    metadata JSONB DEFAULT '{}',
    created_by UUID REFERENCES users(id),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Test Events for tracking individual events during tests
CREATE TABLE test_events (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    organization_id UUID REFERENCES organizations(id) ON DELETE CASCADE,
    execution_id UUID REFERENCES test_executions(id) ON DELETE CASCADE,
    event_id UUID, -- Reference to event_store.id if applicable
    event_type VARCHAR(100) NOT NULL,
    event_data JSONB NOT NULL,
    expected_data JSONB,
    status VARCHAR(50) DEFAULT 'pending', -- 'pending', 'received', 'matched', 'failed', 'timeout'
    latency_ms INTEGER,
    error_message TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    received_at TIMESTAMP,
    matched_at TIMESTAMP
);

-- Event Templates for reusable test patterns
CREATE TABLE event_templates (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    organization_id UUID REFERENCES organizations(id) ON DELETE CASCADE,
    name VARCHAR(255) NOT NULL,
    description TEXT,
    event_type VARCHAR(100) NOT NULL,
    template_data JSONB NOT NULL,
    variables JSONB DEFAULT '[]', -- Array of variable definitions
    is_public BOOLEAN DEFAULT false,
    created_by UUID REFERENCES users(id),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Test Monitors for continuous monitoring
CREATE TABLE test_monitors (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    organization_id UUID REFERENCES organizations(id) ON DELETE CASCADE,
    name VARCHAR(255) NOT NULL,
    description TEXT,
    event_patterns TEXT[] NOT NULL,
    conditions JSONB NOT NULL,
    actions JSONB DEFAULT '[]', -- Actions to take when conditions are met
    is_active BOOLEAN DEFAULT true,
    alert_threshold INTEGER DEFAULT 5, -- Number of failures before alerting
    current_failures INTEGER DEFAULT 0,
    last_triggered TIMESTAMP,
    created_by UUID REFERENCES users(id),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Test Reports for aggregated results
CREATE TABLE test_reports (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    organization_id UUID REFERENCES organizations(id) ON DELETE CASCADE,
    name VARCHAR(255) NOT NULL,
    report_type VARCHAR(50) NOT NULL, -- 'execution', 'performance', 'reliability'
    time_range JSONB NOT NULL, -- {start, end}
    filters JSONB DEFAULT '{}',
    data JSONB NOT NULL,
    generated_by UUID REFERENCES users(id),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Enhanced permissions for testing environment
INSERT INTO permissions (name, description, module, action, resource) VALUES
('testing.create', 'Create test scenarios', 'testing', 'create', 'scenario'),
('testing.read', 'View test scenarios and results', 'testing', 'read', 'scenario'),
('testing.update', 'Modify test scenarios', 'testing', 'update', 'scenario'),
('testing.delete', 'Delete test scenarios', 'testing', 'delete', 'scenario'),
('testing.execute', 'Execute test scenarios', 'testing', 'execute', 'scenario'),
('testing.monitor', 'Access monitoring features', 'testing', 'monitor', 'system'),
('testing.reports', 'Generate and view reports', 'testing', 'reports', 'data'),
('testing.templates', 'Manage event templates', 'testing', 'manage', 'template'),
('testing.admin', 'Full testing environment access', 'testing', 'admin', '*');

-- Add testing permissions to existing roles
INSERT INTO role_permissions (role_id, permission_id)
SELECT r.id, p.id FROM roles r, permissions p 
WHERE r.name = 'Super Admin' AND p.module = 'testing';

INSERT INTO role_permissions (role_id, permission_id)
SELECT r.id, p.id FROM roles r, permissions p 
WHERE r.name = 'Admin' AND p.module = 'testing';

INSERT INTO role_permissions (role_id, permission_id)
SELECT r.id, p.id FROM roles r, permissions p 
WHERE r.name = 'Developer' AND p.module = 'testing' AND p.name != 'testing.admin';

-- Indexes for performance
CREATE INDEX idx_test_scenarios_org_category ON test_scenarios(organization_id, category);
CREATE INDEX idx_test_executions_scenario_status ON test_executions(scenario_id, status);
CREATE INDEX idx_test_events_execution_status ON test_events(execution_id, status);
CREATE INDEX idx_test_monitors_org_active ON test_monitors(organization_id, is_active);
CREATE INDEX idx_test_reports_org_type ON test_reports(organization_id, report_type, created_at);

-- Row Level Security
CREATE POLICY test_scenarios_org_isolation ON test_scenarios FOR ALL TO authenticated 
USING (organization_id = current_setting('app.current_organization_id')::uuid);

CREATE POLICY test_executions_org_isolation ON test_executions FOR ALL TO authenticated 
USING (organization_id = current_setting('app.current_organization_id')::uuid);

CREATE POLICY test_events_org_isolation ON test_events FOR ALL TO authenticated 
USING (organization_id = current_setting('app.current_organization_id')::uuid);

CREATE POLICY event_templates_org_isolation ON event_templates FOR ALL TO authenticated 
USING (organization_id = current_setting('app.current_organization_id')::uuid);

CREATE POLICY test_monitors_org_isolation ON test_monitors FOR ALL TO authenticated 
USING (organization_id = current_setting('app.current_organization_id')::uuid);

CREATE POLICY test_reports_org_isolation ON test_reports FOR ALL TO authenticated 
USING (organization_id = current_setting('app.current_organization_id')::uuid);

-- Enable RLS
ALTER TABLE test_scenarios ENABLE ROW LEVEL SECURITY;
ALTER TABLE test_executions ENABLE ROW LEVEL SECURITY;
ALTER TABLE test_events ENABLE ROW LEVEL SECURITY;
ALTER TABLE event_templates ENABLE ROW LEVEL SECURITY;
ALTER TABLE test_monitors ENABLE ROW LEVEL SECURITY;
ALTER TABLE test_reports ENABLE ROW LEVEL SECURITY;
