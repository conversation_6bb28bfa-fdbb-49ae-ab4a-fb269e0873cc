-- Enhanced Multi-Tenant Architecture and RBAC System

-- Add tenant isolation and resource quotas to organizations
ALTER TABLE organizations ADD COLUMN IF NOT EXISTS domain VARCHAR(255);
ALTER TABLE organizations ADD COLUMN IF NOT EXISTS branding JSONB DEFAULT '{}';
ALTER TABLE organizations ADD COLUMN IF NOT EXISTS resource_quotas JSONB DEFAULT '{
  "agents": 10,
  "tools": 50,
  "documents": 1000,
  "api_calls_per_month": 10000,
  "storage_gb": 5
}';
ALTER TABLE organizations ADD COLUMN IF NOT EXISTS current_usage JSONB DEFAULT '{
  "agents": 0,
  "tools": 0,
  "documents": 0,
  "api_calls_this_month": 0,
  "storage_used_gb": 0
}';
ALTER TABLE organizations ADD COLUMN IF NOT EXISTS billing_status VARCHAR(50) DEFAULT 'active';

-- Enhanced roles with hierarchy and feature permissions
DROP TABLE IF EXISTS permissions CASCADE;
CREATE TABLE permissions (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    name VARCHAR(100) UNIQUE NOT NULL,
    description TEXT,
    module VARCHAR(50) NOT NULL, -- 'agents', 'tools', 'hitl', etc.
    action VARCHAR(50) NOT NULL, -- 'create', 'read', 'update', 'delete', 'execute'
    resource VARCHAR(50), -- specific resource type if applicable
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Role hierarchy enum
CREATE TYPE role_hierarchy AS ENUM ('SUPER_ADMIN', 'ORG_ADMIN', 'DEVELOPER', 'APPROVER', 'VIEWER');

-- Update roles table with hierarchy
ALTER TABLE roles ADD COLUMN IF NOT EXISTS hierarchy role_hierarchy;
ALTER TABLE roles ADD COLUMN IF NOT EXISTS is_default BOOLEAN DEFAULT false;

-- Role-Permission mapping
CREATE TABLE role_permissions (
    role_id UUID REFERENCES roles(id) ON DELETE CASCADE,
    permission_id UUID REFERENCES permissions(id) ON DELETE CASCADE,
    PRIMARY KEY (role_id, permission_id)
);

-- API Keys for external integrations
CREATE TABLE api_keys (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    organization_id UUID REFERENCES organizations(id) ON DELETE CASCADE,
    name VARCHAR(255) NOT NULL,
    key_hash VARCHAR(255) NOT NULL UNIQUE,
    key_prefix VARCHAR(20) NOT NULL, -- First few chars for identification
    permissions JSONB DEFAULT '[]',
    rate_limit INTEGER DEFAULT 1000, -- requests per hour
    expires_at TIMESTAMP,
    last_used_at TIMESTAMP,
    is_active BOOLEAN DEFAULT true,
    created_by UUID REFERENCES users(id),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Event Store for APIX Real-Time Engine
CREATE TABLE event_store (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    organization_id UUID REFERENCES organizations(id) ON DELETE CASCADE,
    event_type VARCHAR(100) NOT NULL,
    event_version INTEGER DEFAULT 1,
    aggregate_id UUID NOT NULL, -- ID of the entity that generated the event
    aggregate_type VARCHAR(50) NOT NULL, -- 'agent', 'tool', 'user', etc.
    event_data JSONB NOT NULL,
    metadata JSONB DEFAULT '{}',
    user_id UUID REFERENCES users(id),
    session_id VARCHAR(255),
    sequence_number BIGSERIAL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    processed_at TIMESTAMP,
    
    -- Partitioning by organization for performance
    CONSTRAINT event_store_org_partition CHECK (organization_id IS NOT NULL)
);

-- Event Subscriptions for pub/sub system
CREATE TABLE event_subscriptions (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    organization_id UUID REFERENCES organizations(id) ON DELETE CASCADE,
    subscriber_id VARCHAR(255) NOT NULL, -- WebSocket connection ID or service name
    event_patterns TEXT[] NOT NULL, -- Array of event patterns to match
    filters JSONB DEFAULT '{}',
    is_active BOOLEAN DEFAULT true,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    last_heartbeat TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Notification Templates and Delivery
CREATE TABLE notification_templates (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    organization_id UUID REFERENCES organizations(id) ON DELETE CASCADE,
    name VARCHAR(255) NOT NULL,
    event_type VARCHAR(100) NOT NULL,
    channels TEXT[] DEFAULT ARRAY['websocket'], -- 'websocket', 'email', 'slack', 'webhook'
    template JSONB NOT NULL,
    is_active BOOLEAN DEFAULT true,
    created_by UUID REFERENCES users(id),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

CREATE TABLE notifications (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    organization_id UUID REFERENCES organizations(id) ON DELETE CASCADE,
    user_id UUID REFERENCES users(id),
    type VARCHAR(100) NOT NULL,
    title VARCHAR(255) NOT NULL,
    message TEXT,
    data JSONB DEFAULT '{}',
    channels TEXT[] DEFAULT ARRAY['websocket'],
    status VARCHAR(50) DEFAULT 'pending', -- 'pending', 'sent', 'delivered', 'failed'
    read_at TIMESTAMP,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    sent_at TIMESTAMP
);

-- Billing and Usage Tracking
CREATE TABLE billing_events (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    organization_id UUID REFERENCES organizations(id) ON DELETE CASCADE,
    event_type VARCHAR(100) NOT NULL, -- 'api_call', 'agent_execution', 'storage_usage'
    quantity INTEGER DEFAULT 1,
    unit_cost DECIMAL(10,4) DEFAULT 0,
    total_cost DECIMAL(10,2) DEFAULT 0,
    metadata JSONB DEFAULT '{}',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Insert comprehensive permissions
INSERT INTO permissions (name, description, module, action, resource) VALUES
-- Agent permissions
('agents.create', 'Create new agents', 'agents', 'create', 'agent'),
('agents.read', 'View agents', 'agents', 'read', 'agent'),
('agents.update', 'Modify agents', 'agents', 'update', 'agent'),
('agents.delete', 'Delete agents', 'agents', 'delete', 'agent'),
('agents.execute', 'Execute agents', 'agents', 'execute', 'agent'),
('agents.publish', 'Publish agents', 'agents', 'publish', 'agent'),

-- Tool permissions
('tools.create', 'Create new tools', 'tools', 'create', 'tool'),
('tools.read', 'View tools', 'tools', 'read', 'tool'),
('tools.update', 'Modify tools', 'tools', 'update', 'tool'),
('tools.delete', 'Delete tools', 'tools', 'delete', 'tool'),
('tools.execute', 'Execute tools', 'tools', 'execute', 'tool'),

-- Provider permissions
('providers.create', 'Create AI providers', 'providers', 'create', 'provider'),
('providers.read', 'View providers', 'providers', 'read', 'provider'),
('providers.update', 'Modify providers', 'providers', 'update', 'provider'),
('providers.delete', 'Delete providers', 'providers', 'delete', 'provider'),

-- HITL permissions
('hitl.create', 'Create approval requests', 'hitl', 'create', 'request'),
('hitl.read', 'View approval requests', 'hitl', 'read', 'request'),
('hitl.approve', 'Approve requests', 'hitl', 'approve', 'request'),
('hitl.reject', 'Reject requests', 'hitl', 'reject', 'request'),

-- Knowledge permissions
('knowledge.create', 'Create documents', 'knowledge', 'create', 'document'),
('knowledge.read', 'View documents', 'knowledge', 'read', 'document'),
('knowledge.update', 'Modify documents', 'knowledge', 'update', 'document'),
('knowledge.delete', 'Delete documents', 'knowledge', 'delete', 'document'),

-- Widget permissions
('widgets.create', 'Create widgets', 'widgets', 'create', 'widget'),
('widgets.read', 'View widgets', 'widgets', 'read', 'widget'),
('widgets.update', 'Modify widgets', 'widgets', 'update', 'widget'),
('widgets.delete', 'Delete widgets', 'widgets', 'delete', 'widget'),
('widgets.embed', 'Embed widgets', 'widgets', 'embed', 'widget'),

-- Analytics permissions
('analytics.read', 'View analytics', 'analytics', 'read', 'data'),
('analytics.export', 'Export analytics', 'analytics', 'export', 'data'),

-- User management permissions
('users.create', 'Create users', 'users', 'create', 'user'),
('users.read', 'View users', 'users', 'read', 'user'),
('users.update', 'Modify users', 'users', 'update', 'user'),
('users.delete', 'Delete users', 'users', 'delete', 'user'),
('users.invite', 'Invite users', 'users', 'invite', 'user'),

-- Organization permissions
('organization.read', 'View organization', 'organization', 'read', 'settings'),
('organization.update', 'Modify organization', 'organization', 'update', 'settings'),
('organization.billing', 'Manage billing', 'organization', 'manage', 'billing'),

-- API key permissions
('api_keys.create', 'Create API keys', 'api_keys', 'create', 'key'),
('api_keys.read', 'View API keys', 'api_keys', 'read', 'key'),
('api_keys.delete', 'Delete API keys', 'api_keys', 'delete', 'key'),

-- System permissions
('system.admin', 'System administration', 'system', 'admin', '*');

-- Update existing roles with hierarchy and permissions
UPDATE roles SET hierarchy = 'SUPER_ADMIN' WHERE name = 'Super Admin';
UPDATE roles SET hierarchy = 'ORG_ADMIN' WHERE name = 'Admin';
UPDATE roles SET hierarchy = 'DEVELOPER' WHERE name = 'Developer';
UPDATE roles SET hierarchy = 'APPROVER' WHERE name = 'Approver';
UPDATE roles SET hierarchy = 'VIEWER' WHERE name = 'Viewer';

-- Create default role permission mappings
-- Super Admin gets all permissions
INSERT INTO role_permissions (role_id, permission_id)
SELECT r.id, p.id FROM roles r, permissions p 
WHERE r.name = 'Super Admin';

-- Org Admin gets most permissions except system admin
INSERT INTO role_permissions (role_id, permission_id)
SELECT r.id, p.id FROM roles r, permissions p 
WHERE r.name = 'Admin' AND p.name != 'system.admin';

-- Developer gets agent, tool, and sandbox permissions
INSERT INTO role_permissions (role_id, permission_id)
SELECT r.id, p.id FROM roles r, permissions p 
WHERE r.name = 'Developer' 
AND (p.module IN ('agents', 'tools', 'providers', 'knowledge') 
     OR p.name IN ('analytics.read'));

-- Approver gets HITL and read permissions
INSERT INTO role_permissions (role_id, permission_id)
SELECT r.id, p.id FROM roles r, permissions p 
WHERE r.name = 'Approver' 
AND (p.module = 'hitl' OR p.action = 'read');

-- Viewer gets only read permissions
INSERT INTO role_permissions (role_id, permission_id)
SELECT r.id, p.id FROM roles r, permissions p 
WHERE r.name = 'Viewer' AND p.action = 'read';

-- Indexes for performance
CREATE INDEX idx_event_store_org_type_created ON event_store(organization_id, event_type, created_at);
CREATE INDEX idx_event_store_aggregate ON event_store(aggregate_type, aggregate_id);
CREATE INDEX idx_event_store_sequence ON event_store(sequence_number);
CREATE INDEX idx_event_subscriptions_org_active ON event_subscriptions(organization_id, is_active);
CREATE INDEX idx_notifications_user_status ON notifications(user_id, status, created_at);
CREATE INDEX idx_billing_events_org_created ON billing_events(organization_id, created_at);
CREATE INDEX idx_api_keys_org_active ON api_keys(organization_id, is_active);

-- Row Level Security policies for multi-tenant isolation
CREATE POLICY org_isolation_policy ON organizations FOR ALL TO authenticated USING (true);

-- Users can only see users in their organization
CREATE POLICY users_org_isolation ON users FOR ALL TO authenticated 
USING (organization_id = current_setting('app.current_organization_id')::uuid);

-- All other tables follow the same pattern
CREATE POLICY agents_org_isolation ON agents FOR ALL TO authenticated 
USING (organization_id = current_setting('app.current_organization_id')::uuid);

CREATE POLICY tools_org_isolation ON tools FOR ALL TO authenticated 
USING (organization_id = current_setting('app.current_organization_id')::uuid);

CREATE POLICY providers_org_isolation ON providers FOR ALL TO authenticated 
USING (organization_id = current_setting('app.current_organization_id')::uuid);

CREATE POLICY hitl_requests_org_isolation ON hitl_requests FOR ALL TO authenticated 
USING (organization_id = current_setting('app.current_organization_id')::uuid);

CREATE POLICY documents_org_isolation ON documents FOR ALL TO authenticated 
USING (organization_id = current_setting('app.current_organization_id')::uuid);

CREATE POLICY widgets_org_isolation ON widgets FOR ALL TO authenticated 
USING (organization_id = current_setting('app.current_organization_id')::uuid);

CREATE POLICY analytics_events_org_isolation ON analytics_events FOR ALL TO authenticated 
USING (organization_id = current_setting('app.current_organization_id')::uuid);

CREATE POLICY event_store_org_isolation ON event_store FOR ALL TO authenticated 
USING (organization_id = current_setting('app.current_organization_id')::uuid);

CREATE POLICY notifications_org_isolation ON notifications FOR ALL TO authenticated 
USING (organization_id = current_setting('app.current_organization_id')::uuid);

CREATE POLICY billing_events_org_isolation ON billing_events FOR ALL TO authenticated 
USING (organization_id = current_setting('app.current_organization_id')::uuid);
