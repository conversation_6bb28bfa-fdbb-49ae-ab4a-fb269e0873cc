// Shared types for the entire SynapseAI platform

export interface Organization {
  id: string
  name: string
  slug: string
  settings: Record<string, any>
  subscriptionTier: "free" | "pro" | "enterprise"
  createdAt: Date
  updatedAt: Date
}

export interface User {
  id: string
  organizationId: string
  email: string
  firstName?: string
  lastName?: string
  avatarUrl?: string
  isActive: boolean
  emailVerified: boolean
  lastLogin?: Date
  createdAt: Date
  updatedAt: Date
  roles?: Role[]
}

export interface Role {
  id: string
  organizationId: string
  name: string
  description?: string
  permissions: string[]
  isSystem: boolean
  createdAt: Date
}

export interface Session {
  id: string
  userId: string
  tokenHash: string
  expiresAt: Date
  ipAddress?: string
  userAgent?: string
  isActive: boolean
  createdAt: Date
}

export interface Provider {
  id: string
  organizationId: string
  name: string
  type: "openai" | "anthropic" | "groq" | "xai" | "deepinfra"
  config: Record<string, any>
  isActive: boolean
  createdBy: string
  createdAt: Date
  updatedAt: Date
}

export interface Agent {
  id: string
  organizationId: string
  name: string
  description?: string
  systemPrompt?: string
  model?: string
  providerId?: string
  config: Record<string, any>
  status: "draft" | "active" | "paused"
  version: number
  createdBy: string
  createdAt: Date
  updatedAt: Date
  provider?: Provider
  tools?: Tool[]
}

export interface Tool {
  id: string
  organizationId: string
  name: string
  description?: string
  type: "function" | "api" | "webhook"
  schema: Record<string, any>
  implementation: Record<string, any>
  isPublic: boolean
  createdBy: string
  createdAt: Date
  updatedAt: Date
}

export interface HITLRequest {
  id: string
  organizationId: string
  agentId?: string
  sessionId?: string
  type: "approval" | "input" | "review"
  title: string
  description?: string
  context: Record<string, any>
  status: "pending" | "approved" | "rejected" | "completed"
  priority: "low" | "medium" | "high" | "urgent"
  assignedTo?: string
  response?: Record<string, any>
  createdBy: string
  resolvedBy?: string
  resolvedAt?: Date
  createdAt: Date
  updatedAt: Date
  agent?: Agent
  assignee?: User
}

export interface Document {
  id: string
  organizationId: string
  title: string
  content: string
  contentType: string
  metadata: Record<string, any>
  embeddings?: number[]
  tags: string[]
  status: "active" | "archived"
  createdBy: string
  createdAt: Date
  updatedAt: Date
}

export interface Widget {
  id: string
  organizationId: string
  name: string
  description?: string
  type: "chat" | "form" | "embed"
  config: Record<string, any>
  agentId?: string
  isPublic: boolean
  embedCode?: string
  createdBy: string
  createdAt: Date
  updatedAt: Date
  agent?: Agent
}

export interface AnalyticsEvent {
  id: string
  organizationId: string
  eventType: string
  entityType?: string
  entityId?: string
  userId?: string
  sessionId?: string
  properties: Record<string, any>
  timestamp: Date
}

export interface Sandbox {
  id: string
  organizationId: string
  name: string
  description?: string
  type: "agent" | "tool" | "workflow"
  config: Record<string, any>
  status: "active" | "paused" | "stopped"
  createdBy: string
  createdAt: Date
  updatedAt: Date
}

export interface Conversation {
  id: string
  organizationId: string
  agentId?: string
  userId?: string
  widgetId?: string
  sessionId: string
  title?: string
  status: "active" | "completed" | "archived"
  metadata: Record<string, any>
  createdAt: Date
  updatedAt: Date
  messages?: Message[]
}

export interface Message {
  id: string
  conversationId: string
  role: "user" | "assistant" | "system"
  content: string
  metadata: Record<string, any>
  createdAt: Date
}

// WebSocket Event Types
export interface WSEvent {
  type: string
  payload: any
  timestamp: Date
  userId?: string
  organizationId: string
}

// API Response Types
export interface ApiResponse<T = any> {
  success: boolean
  data?: T
  error?: string
  message?: string
  meta?: {
    total?: number
    page?: number
    limit?: number
  }
}

// Authentication Types
export interface AuthTokens {
  accessToken: string
  refreshToken: string
  expiresIn: number
}

export interface JWTPayload {
  sub: string
  email: string
  organizationId: string
  roles: string[]
  iat: number
  exp: number
}

// Permission Types
export type Permission =
  | "agents.create"
  | "agents.read"
  | "agents.update"
  | "agents.delete"
  | "tools.create"
  | "tools.read"
  | "tools.update"
  | "tools.delete"
  | "providers.create"
  | "providers.read"
  | "providers.update"
  | "providers.delete"
  | "hitl.create"
  | "hitl.read"
  | "hitl.update"
  | "hitl.delete"
  | "documents.create"
  | "documents.read"
  | "documents.update"
  | "documents.delete"
  | "widgets.create"
  | "widgets.read"
  | "widgets.update"
  | "widgets.delete"
  | "analytics.read"
  | "users.create"
  | "users.read"
  | "users.update"
  | "users.delete"
  | "sandboxes.create"
  | "sandboxes.read"
  | "sandboxes.update"
  | "sandboxes.delete"
  | "*"
