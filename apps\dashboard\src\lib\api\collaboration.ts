import { apiClient } from "./client"

export const collaborationApi = {
  // Sharing
  shareScenario: (scenarioId: string, data: any) =>
    apiClient.post(`/testing/collaboration/scenarios/${scenarioId}/share`, data),
  getScenarioShares: (scenarioId: string) => apiClient.get(`/testing/collaboration/scenarios/${scenarioId}/shares`),
  removeScenarioShare: (shareId: string) => apiClient.delete(`/testing/collaboration/shares/${shareId}`),

  // Comments
  addComment: (scenarioId: string, data: any) =>
    apiClient.post(`/testing/collaboration/scenarios/${scenarioId}/comments`, data),
  getScenarioComments: (scenarioId: string) => apiClient.get(`/testing/collaboration/scenarios/${scenarioId}/comments`),
  resolveComment: (commentId: string) => apiClient.put(`/testing/collaboration/comments/${commentId}/resolve`),

  // Versions
  createScenarioVersion: (scenarioId: string, data: any) =>
    apiClient.post(`/testing/collaboration/scenarios/${scenarioId}/versions`, data),
  getScenarioVersions: (scenarioId: string) => apiClient.get(`/testing/collaboration/scenarios/${scenarioId}/versions`),

  // Favorites
  toggleFavorite: (scenarioId: string) => apiClient.post(`/testing/collaboration/scenarios/${scenarioId}/favorite`),

  // Real-time Collaboration
  joinCollaborationSession: (scenarioId: string) =>
    apiClient.post(`/testing/collaboration/scenarios/${scenarioId}/collaborate/join`),
  leaveCollaborationSession: (scenarioId: string) =>
    apiClient.post(`/testing/collaboration/scenarios/${scenarioId}/collaborate/leave`),
  updateCursorPosition: (scenarioId: string, position: any) =>
    apiClient.put(`/testing/collaboration/scenarios/${scenarioId}/collaborate/cursor`, { position }),
}
