import { apiClient } from "./client"

export const agentsApi = {
  // Existing methods...
  create: async (agentData: any) => {
    const response = await apiClient.post("/agents", agentData)
    return response.data
  },

  getAll: async (filters: any = {}) => {
    const response = await apiClient.get("/agents", { params: filters })
    return response.data
  },

  getById: async (id: string) => {
    const response = await apiClient.get(`/agents/${id}`)
    return response.data
  },

  update: async (id: string, updates: any) => {
    const response = await apiClient.put(`/agents/${id}`, updates)
    return response.data
  },

  delete: async (id: string) => {
    const response = await apiClient.delete(`/agents/${id}`)
    return response.data
  },

  executeAgent: async (id: string, execution: any) => {
    const response = await apiClient.post(`/agents/${id}/execute`, execution)
    return response.data
  },

  // New methods for agent builder
  generateConfiguration: async (description: string) => {
    const response = await apiClient.post("/agents/generate-config", { description })
    return response.data
  },

  getProviders: async () => {
    const response = await apiClient.get("/providers")
    return response.data
  },

  getVersions: async (agentId: string) => {
    const response = await apiClient.get(`/agents/${agentId}/versions`)
    return response.data
  },

  revertToVersion: async (agentId: string, version: number) => {
    const response = await apiClient.post(`/agents/${agentId}/revert/${version}`)
    return response.data
  },

  getAnalytics: async (agentId: string, timeRange = "7d") => {
    const response = await apiClient.get(`/agents/${agentId}/analytics`, {
      params: { timeRange },
    })
    return response.data
  },

  getExecutions: async (agentId: string, filters: any = {}) => {
    const response = await apiClient.get(`/agents/${agentId}/executions`, {
      params: filters,
    })
    return response.data
  },
}

export const promptTemplatesApi = {
  getTemplates: async (filters: any = {}) => {
    const response = await apiClient.get("/prompt-templates", { params: filters })
    return response.data
  },

  create: async (templateData: any) => {
    const response = await apiClient.post("/prompt-templates", templateData)
    return response.data
  },

  update: async (id: string, updates: any) => {
    const response = await apiClient.put(`/prompt-templates/${id}`, updates)
    return response.data
  },

  delete: async (id: string) => {
    const response = await apiClient.delete(`/prompt-templates/${id}`)
    return response.data
  },

  getVersions: async (templateId: string) => {
    const response = await apiClient.get(`/prompt-templates/${templateId}/versions`)
    return response.data
  },

  preview: async (templateContent: string, variables: any[], values: any) => {
    const response = await apiClient.post("/prompt-templates/preview", {
      templateContent,
      variables,
      values,
    })
    return response.data
  },
}
