import type { ApiResponse } from "@synapseai/shared/types"

const API_BASE_URL = process.env.NEXT_PUBLIC_API_URL || "http://localhost:3001/api/v1"

interface RequestConfig extends RequestInit {
  retry?: number
  timeout?: number
}

class ApiClient {
  private baseURL: string
  private defaultTimeout = 30000 // 30 seconds

  constructor(baseURL: string) {
    this.baseURL = baseURL
  }

  private async request<T>(endpoint: string, options: RequestConfig = {}): Promise<ApiResponse<T>> {
    const { retry = 3, timeout = this.defaultTimeout, ...requestOptions } = options
    const url = `${this.baseURL}${endpoint}`

    const token = this.getToken()
    const headers: HeadersInit = {
      "Content-Type": "application/json",
      ...requestOptions.headers,
    }

    if (token) {
      headers.Authorization = `Bearer ${token}`
    }

    // Add organization context if available
    const organizationId = this.getOrganizationId()
    if (organizationId) {
      headers["X-Organization-ID"] = organizationId
    }

    const controller = new AbortController()
    const timeoutId = setTimeout(() => controller.abort(), timeout)

    let lastError: Error

    for (let attempt = 0; attempt <= retry; attempt++) {
      try {
        const response = await fetch(url, {
          ...requestOptions,
          headers,
          signal: controller.signal,
        })

        clearTimeout(timeoutId)

        if (!response.ok) {
          await this.handleErrorResponse(response)
        }

        const data = await response.json()
        return data
      } catch (error) {
        lastError = error as Error

        // Don't retry on certain errors
        if (this.shouldNotRetry(error as Error)) {
          break
        }

        // Wait before retrying (exponential backoff)
        if (attempt < retry) {
          await this.delay(Math.pow(2, attempt) * 1000)
        }
      }
    }

    clearTimeout(timeoutId)
    throw lastError!
  }

  private async handleErrorResponse(response: Response): Promise<never> {
    let errorData: any = {}

    try {
      errorData = await response.json()
    } catch {
      // Response body is not JSON
    }

    switch (response.status) {
      case 401:
        await this.handleUnauthorized()
        throw new Error(errorData.message || "Authentication required")

      case 403:
        throw new Error(errorData.message || "Access forbidden")

      case 404:
        throw new Error(errorData.message || "Resource not found")

      case 429:
        throw new Error(errorData.message || "Rate limit exceeded")

      case 500:
        throw new Error(errorData.message || "Internal server error")

      default:
        throw new Error(errorData.message || `HTTP ${response.status}`)
    }
  }

  private async handleUnauthorized(): Promise<void> {
    const refreshToken = localStorage.getItem("refreshToken")

    if (refreshToken) {
      try {
        const response = await fetch(`${this.baseURL}/auth/refresh`, {
          method: "POST",
          headers: {
            "Content-Type": "application/json",
          },
          body: JSON.stringify({ refreshToken }),
        })

        if (response.ok) {
          const data = await response.json()
          localStorage.setItem("accessToken", data.data.accessToken)
          localStorage.setItem("refreshToken", data.data.refreshToken)
          return
        }
      } catch (error) {
        console.error("Token refresh failed:", error)
      }
    }

    // Clear tokens and redirect to login
    localStorage.removeItem("accessToken")
    localStorage.removeItem("refreshToken")

    if (typeof window !== "undefined") {
      window.location.href = "/login"
    }
  }

  private shouldNotRetry(error: Error): boolean {
    // Don't retry on authentication errors, client errors, etc.
    return error.message.includes("401") || error.message.includes("403") || error.message.includes("400")
  }

  private delay(ms: number): Promise<void> {
    return new Promise((resolve) => setTimeout(resolve, ms))
  }

  private getToken(): string | null {
    if (typeof window === "undefined") return null
    return localStorage.getItem("accessToken")
  }

  private getOrganizationId(): string | null {
    if (typeof window === "undefined") return null

    const token = this.getToken()
    if (!token) return null

    try {
      const payload = JSON.parse(atob(token.split(".")[1]))
      return payload.organizationId
    } catch {
      return null
    }
  }

  async get<T>(endpoint: string, config?: RequestConfig): Promise<ApiResponse<T>> {
    return this.request<T>(endpoint, { method: "GET", ...config })
  }

  async post<T>(endpoint: string, data?: any, config?: RequestConfig): Promise<ApiResponse<T>> {
    return this.request<T>(endpoint, {
      method: "POST",
      body: data ? JSON.stringify(data) : undefined,
      ...config,
    })
  }

  async put<T>(endpoint: string, data?: any, config?: RequestConfig): Promise<ApiResponse<T>> {
    return this.request<T>(endpoint, {
      method: "PUT",
      body: data ? JSON.stringify(data) : undefined,
      ...config,
    })
  }

  async patch<T>(endpoint: string, data?: any, config?: RequestConfig): Promise<ApiResponse<T>> {
    return this.request<T>(endpoint, {
      method: "PATCH",
      body: data ? JSON.stringify(data) : undefined,
      ...config,
    })
  }

  async delete<T>(endpoint: string, config?: RequestConfig): Promise<ApiResponse<T>> {
    return this.request<T>(endpoint, { method: "DELETE", ...config })
  }

  // Upload files
  async upload<T>(endpoint: string, file: File, config?: RequestConfig): Promise<ApiResponse<T>> {
    const formData = new FormData()
    formData.append("file", file)

    const token = this.getToken()
    const headers: HeadersInit = {
      ...config?.headers,
    }

    if (token) {
      headers.Authorization = `Bearer ${token}`
    }

    const organizationId = this.getOrganizationId()
    if (organizationId) {
      headers["X-Organization-ID"] = organizationId
    }

    return this.request<T>(endpoint, {
      method: "POST",
      body: formData,
      headers,
      ...config,
    })
  }
}

export const apiClient = new ApiClient(API_BASE_URL)
